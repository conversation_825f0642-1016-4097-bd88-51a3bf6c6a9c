// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import moment from 'moment-timezone';
import React, { useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Dimensions, Image, Text, TouchableOpacity, View } from 'react-native';

import { leaveCall, leaveCallConfirmation } from '@calls/actions/calls';
import { leaveAndJoinWithAlert, showLimitRestrictedAlert } from '@calls/alerts';
import { setJoiningChannelId } from '@calls/state';
import CompassIcon from '@components/compass_icon';
import FormattedRelativeTime from '@components/formatted_relative_time';
import FormattedText from '@components/formatted_text';
import FormattedTime from '@components/formatted_time';
import Loading from '@components/loading';
import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';
import { getUserTimezone } from '@utils/user';

import type { LimitRestrictedInfo } from '@calls/observers';
import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';
import { PhoneArrowDownLeftIcon } from 'react-native-heroicons/solid'
import MessageCornerSvg from '@app/components/post_list/post/body/message/message_corner_svg';
import { useAlert } from '@app/context/alert';

type Props = {
    post: PostModel;
    isMilitaryTime: boolean;
    joiningChannelId: string | null;
    otherParticipants: boolean;
    isAdmin: boolean;
    isHost: boolean;
    currentUser?: UserModel;
    limitRestrictedInfo?: LimitRestrictedInfo;
    ccChannelId?: string;
    isCurrentUser: boolean
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        title: {
            ...typography('Heading', 500),
            color: theme.centerChannelColor,
        },
        joinCallIcon: {
            backgroundColor: theme.onlineIndicator,
        },
        phoneHangupIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.72),
            backgroundColor: theme.buttonBg,

        },
        callButton: {
            flexDirection: 'row',
            paddingHorizontal: 16,
            paddingVertical: 10,
            gap: 7,
            borderRadius: 4,
            alignItems: 'center',
            alignContent: 'center',
        },
        joinCallButton: {
            backgroundColor: theme.onlineIndicator,
        },
        leaveCallButton: {
            backgroundColor: theme.dndIndicator,
        },
        buttonText: {
            color: theme.buttonColor,
            ...typography('Heading', 75, 'SemiBold'),
        },
        buttonRestricted: {
            color: changeOpacity(theme.centerChannelColor, 0.32),            
        },
        buttonIcon: {
            color: theme.buttonColor,
        },
        joinCallButtonRestricted: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.08),
        },
        endCallInfo: {
            flexDirection: 'row',
            alignItems: 'center',
            alignContent: 'center',
        },
        separator: {
            color: theme.centerChannelColor,
            marginLeft: 5,
            marginRight: 5,
        },
    };
});

export const CallsCustomMessage = ({
    post,
    currentUser,
    isMilitaryTime,
    ccChannelId,
    limitRestrictedInfo,
    joiningChannelId,
    otherParticipants,
    isAdmin,
    isHost,
    isCurrentUser
}: Props) => {
    const intl = useIntl();
    const theme = useTheme();
    const style = getStyleSheet(theme);
    const serverUrl = useServerUrl();
    const timezone = getUserTimezone(currentUser);
    const windowWidth = Dimensions.get('window').width;
    const joiningThisCall = Boolean(joiningChannelId === post.channelId);
    const alreadyInTheCall = Boolean(ccChannelId && ccChannelId === post.channelId);
    const isLimitRestricted = Boolean(limitRestrictedInfo?.limitRestricted);
    const joiningMsg = intl.formatMessage({ id: 'mobile.calls_joining', defaultMessage: 'Joining...' });

    const {showAlert} = useAlert();


    let elementWidth = (windowWidth / 4) * 3

    // console.log(`\n\n\n\nthis the element width ${elementWidth}\n\n\n\n\n`)
    const joinHandler = useCallback(async () => {
        if (isLimitRestricted) {
            showLimitRestrictedAlert(limitRestrictedInfo!, intl);
            return;
        }

        setJoiningChannelId(post.channelId);
        await leaveAndJoinWithAlert(intl, serverUrl, post.channelId);
        setJoiningChannelId(null);
    }, [limitRestrictedInfo, intl, serverUrl, post.channelId]);

    // const leaveCallHandler = useCallback(() => {
    //     leaveCallConfirmation(intl, otherParticipants, isAdmin, isHost, serverUrl, post.channelId);
    // }, [intl, otherParticipants, isAdmin, isHost, serverUrl, post.channelId]);

      const leaveCallHandler =  () => {
        console.log(`\n\nthis end call function\n\n`)
            

        showAlert(
            "تنبيه", "هل تريد مغادرة المكالمة",
            [
                {
                    text: 'الغاء',
                    style: 'cancel',
                    onPress: () => {
                        return;
                    }
                },
                {
                    text: 'مغادرة المكالمة',
                    style: 'default',
                    onPress: () => {
                        leaveCall();
                         return;
                    }
                }
            ]

        )

        //   await leaveAndJoinWithAlert(intl, serverUrl, post.channelId);

    }
    const title = post.props.title ? (
        <Text style={style.title}>
            {post.props.title}
        </Text>
    ) : null;

    if (post.props.end_at) {
        return (
            <>
                {title}
                <View style={{display:"flex",flexDirection: isCurrentUser ? 'row' : 'row-reverse',marginVertical:10}}>
            <View style={{zIndex:-20, marginTop:'auto', transform: [{ scaleX: isCurrentUser ? 1 : -1 }],
                    marginRight:isCurrentUser ? -6 : 0 , marginLeft:!isCurrentUser ? -6 : 0,
                
                }}>
                    <MessageCornerSvg 
                    isCurrentUser={true}
                    color={isCurrentUser ? theme.buttonBg : theme.sidebarTextHoverBg} />
                </View>
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: 12,
                    gap: 8,
                    borderColor: isCurrentUser ? "#00987e" : theme.sidebarTextHoverBg,
                    borderWidth:5,
                    backgroundColor: !isCurrentUser ? changeOpacity(theme.sidebarText, 0.10) : "#009279",
                    borderRadius: 15,
                    height: 65,
                    width: 240,
                }}>

                    <View
                        style={{
                            height: 40, width: 40,
                            backgroundColor: !isCurrentUser ? theme.buttonBg : "white",
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 25
                        }}
                    >
                        <PhoneArrowDownLeftIcon
                            size={20}
                            color={isCurrentUser ? theme.buttonBg :  "white"}
                        />
                    </View>

                    <View style={{
                        display: 'flex',
                        flexDirection: 'row',
                        flex: 1,
                        alignItems: 'center',
                        justifyContent: 'space-between'
                    }}>

                        {/* 
                        <View style={style.endCallInfo}>
                           <FormattedText
                                style={style.timeText}
                                id={'mobile.calls_ended_at'}
                                defaultMessage={'Ended at'}
                            />
                            <Text>{' '}</Text>
                            <FormattedTime
                                style={style.timeText}
                                value={post.props.end_at}
                                isMilitaryTime={isMilitaryTime}
                                timezone={timezone}
                            />
                            <Text style={style.separator}>{'•'}</Text>
                            <FormattedText
                                id={'mobile.calls_lasted'}
                                style={style.timeText}
                                defaultMessage={'Lasted {duration}'}
                                values={{duration: moment.duration(post.props.end_at - post.props.start_at).humanize(false)}}
                            />
                        </View>
                            */}
                        <View style={{ display: 'flex', alignItems: 'flex-start'}}>
                            <FormattedText
                                id={'mobile.calls_call_ended'}
                                defaultMessage={'Call ended'}
                                style={{
                                    color: isCurrentUser ? "white" : changeOpacity(theme.sidebarText, 0.50),
                                    ...typography('Heading', 75),
                                }}
                            />
                            <FormattedTime
                                style={{                                    
                                    color: isCurrentUser ? "white": changeOpacity(theme.sidebarText, 0.50),
                                    ...typography('Heading', 75),

                                }}
                                value={post.props.end_at}
                                isMilitaryTime={isMilitaryTime}
                                timezone={timezone}
                            />
                        </View>


                    </View>

                </View>
                </View>
                
            </>
        );
    }

    const button = alreadyInTheCall ? (
        <TouchableOpacity
            style={[style.callButton, style.leaveCallButton]}
            onPress={leaveCallHandler}
        >
            <CompassIcon
                name='phone-hangup'
                size={18}
                style={[style.buttonIcon]}
            />
            <FormattedText
                id={'mobile.calls_leave'}
                defaultMessage={'Leave'}
                style={style.buttonText}
            />
        </TouchableOpacity>
    ) : (
        <TouchableOpacity
            style={[
                style.callButton,
                {
                    backgroundColor: isCurrentUser ? theme.centerChannelBg : theme.buttonBg
                },
                isLimitRestricted && style.joinCallButtonRestricted,]}
            onPress={joinHandler}
        >
            <CompassIcon
                name='phone-in-talk'
                size={18}
                style={[{
                    color: !isCurrentUser ? "white" : theme.buttonBg
                }, isLimitRestricted && style.buttonRestricted]}
            />
            <FormattedText
                id={'mobile.calls_join'}
                defaultMessage={'Join'}
                style={[{
                    color:  !isCurrentUser ? "white" : theme.buttonBg,
                    ...typography('Heading', 75),
                }, isLimitRestricted && style.buttonRestricted]}
            />
        </TouchableOpacity>
    );

    const joiningButton = (
        <Loading
            color={theme.buttonColor}
            size={'small'}
            footerText={joiningMsg}
            containerStyle={[style.callButton, style.joinCallButton]}
            footerTextStyles={style.buttonText}
        />
    );

    return (
        <>
            {title}
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                // color: changeOpacity(theme.centerChannelColor, 0.6),
                padding: 12,
                marginBottom: 2,
                gap: 8,
                backgroundColor: !isCurrentUser ? changeOpacity(theme.sidebarText, 0.10) : theme.buttonBg,
                borderColor: isCurrentUser ? "#00987e" : theme.sidebarTextHoverBg,
                borderWidth:5,
                borderTopStartRadius: 15,
                borderBottomStartRadius: isCurrentUser ? 0 : 15,
                borderBottomEndRadius: isCurrentUser ? 15 : 0,
                borderTopEndRadius: 15,
                width: (windowWidth / 4) * 3,

                justifyContent: 'space-between'

            }}>
                <CompassIcon
                    name='phone-in-talk'
                    size={20}
                    style={[{
                        padding: 10,
                        borderRadius: 20,
                        color: !isCurrentUser ? "white" : theme.buttonBg,
                        overflow: 'hidden',
                    }, {
                        backgroundColor: isCurrentUser ? "white" : theme.buttonBg

                    }]}
                />
                <View style={{
                    display: 'flex',
                    flexDirection: elementWidth < 310 ? 'column' : 'row',
                    // flex: 1,
                    alignItems: elementWidth < 310 ? undefined : 'center',
                    justifyContent: elementWidth < 310 ? 'center' : undefined
                }}>
                    <FormattedText
                        id={'mobile.calls_started_call'}
                        defaultMessage={'Call started'}
                        style={{
                            color: isCurrentUser ? "white" : changeOpacity(theme.sidebarText, 0.50)
                            ,
                            ...typography('Heading', 75),
                        }}
                    />
                    <FormattedRelativeTime

                        value={post.props.start_at}
                        updateIntervalInSeconds={1}
                        style={{
                            //  marginStart: 15,
                            color:isCurrentUser ? "white": changeOpacity(theme.sidebarText, 0.50)
                            , ...typography('Heading', 75),

                        }}
                    />
                </View>
                {joiningThisCall ? joiningButton : button}
            </View>
        </>
    );
};

